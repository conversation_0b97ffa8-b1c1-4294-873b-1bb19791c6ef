name: Deploy to Production

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Run tests
      run: |
        python -m pytest tests/ -v || echo "No tests found"
        
    - name: Build Docker image
      run: |
        docker build -t common-class-api .
        
    - name: Deploy to Railway
      if: github.ref == 'refs/heads/main'
      run: |
        # Install Railway CLI
        npm install -g @railway/cli
        # Deploy (requires RAILWAY_TOKEN secret)
        railway deploy
      env:
        RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
        
    - name: Health check
      run: |
        sleep 30
        curl -f https://common-class-api.up.railway.app/health || echo "Health check failed"

# 🚀 Deployment Guide for Common Class API

This guide covers multiple deployment options for getting your API running on `common_class_api.ultragent.app`.

## 📋 Prerequisites

- Docker installed
- Git repository (recommended)
- Environment variables configured

## 🎯 Quick Deploy Options

### Option 1: Railway (Recommended - Easy & Fast)

1. **Install Railway CLI:**
   ```bash
   npm install -g @railway/cli
   ```

2. **Login and deploy:**
   ```bash
   railway login
   railway init
   railway up
   ```

3. **Set environment variables:**
   ```bash
   railway variables set API_TOKEN=8a2c1cf846ba5e1245bfb3cc31a6d60a779ca49f02b65ddce1a355e91d01f8bd
   railway variables set LINE_TOKEN=your_line_token
   # Add other tokens as needed
   ```

4. **Custom domain:**
   - Go to Railway dashboard
   - Add custom domain: `common_class_api.ultragent.app`

### Option 2: Render (Free Tier Available)

1. **Connect GitHub repository**
2. **Use the `render.yaml` configuration**
3. **Set custom domain in Render dashboard**

### Option 3: Fly.io

1. **Install Fly CLI:**
   ```bash
   curl -L https://fly.io/install.sh | sh
   ```

2. **Deploy:**
   ```bash
   fly auth login
   fly launch
   fly deploy
   ```

3. **Set secrets:**
   ```bash
   fly secrets set API_TOKEN=8a2c1cf846ba5e1245bfb3cc31a6d60a779ca49f02b65ddce1a355e91d01f8bd
   ```

## 🐳 Docker Deployment

### Local Testing
```bash
docker-compose up --build
```

### Production Server
```bash
# On your server
git clone your-repo
cd common_class_api
chmod +x deploy.sh
./deploy.sh
```

## 🔧 Environment Variables

Make sure these are set in production:
- `API_TOKEN` - Your API authentication token
- `LINE_TOKEN` - LINE Bot Channel Access Token
- `OPENAI_TOKEN` - OpenAI API key (optional)
- `FACEBOOK_TOKEN` - Facebook API token (optional)
- Other service tokens as needed

## 🌐 Domain Setup

### Cloudflare Tunnel (As mentioned in README)

1. **Install cloudflared**
2. **Create tunnel:**
   ```bash
   cloudflared tunnel create common-class-api
   ```
3. **Configure tunnel:**
   ```yaml
   # ~/.cloudflared/config.yml
   tunnel: your-tunnel-id
   credentials-file: /path/to/credentials.json
   
   ingress:
     - hostname: common_class_api.ultragent.app
       service: http://localhost:8000
     - service: http_status:404
   ```

## ✅ Verification

After deployment, test these endpoints:
- `https://common_class_api.ultragent.app/health`
- `https://common_class_api.ultragent.app/docs`
- `https://common_class_api.ultragent.app/available-endpoints`

## 🔍 Troubleshooting

### Common Issues:
1. **500 errors**: Check environment variables
2. **Timeout errors**: Increase health check timeouts
3. **Import errors**: Ensure all dependencies in requirements.txt

### Debug commands:
```bash
# Check logs
docker-compose logs -f

# Test locally
curl http://localhost:8000/health

# Test LINE service
curl -X POST http://localhost:8000/line.test_method \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{"test": true}'
```

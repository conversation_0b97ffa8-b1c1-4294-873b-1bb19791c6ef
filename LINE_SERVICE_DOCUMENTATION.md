# LINE Messaging Service - Complete Documentation

## Overview

The LINE messaging service provides comprehensive integration with the LINE Messaging API, enabling you to send messages, manage users, and handle webhooks through a simple REST API interface.

## Features

✅ **Complete LINE Messaging API Integration**
- Broadcasting to all friends
- Pushing messages to specific users/groups
- Replying to messages with reply tokens
- Multicasting to multiple users
- Rich message types (text, image, video, audio, location, stickers)

✅ **User & Group Management**
- Get user profiles
- Manage group and room memberships
- Leave groups and rooms

✅ **Utility Functions**
- Bot information retrieval
- Quota monitoring
- Webhook signature validation

✅ **Production Ready**
- Comprehensive error handling
- Rate limiting awareness
- Secure token management
- Detailed logging

## Authentication

The service uses your LINE Channel Access Token, which is configured in the `.env` file:

```env
LINE_TOKEN=your_line_channel_access_token_here
```

## API Endpoints

All endpoints follow the pattern: `POST /{service}.{method}`

### Core Messaging

#### 1. Broadcast Messages
Send messages to all friends who have added your LINE Official Account.

```bash
POST /line.broadcast
```

**Example:**
```json
{
  "messages": "Hello everyone! This is a broadcast message."
}
```

**Advanced Example:**
```json
{
  "messages": [
    {
      "type": "text",
      "text": "Hello!"
    },
    {
      "type": "sticker",
      "packageId": "1",
      "stickerId": "1"
    }
  ]
}
```

#### 2. Push Messages
Send messages to a specific user, group, or room.

```bash
POST /line.push
```

**Example:**
```json
{
  "to": "U1234567890abcdef",
  "messages": "Hello specific user!"
}
```

#### 3. Reply to Messages
Reply to a user message using a reply token from webhook events.

```bash
POST /line.reply
```

**Example:**
```json
{
  "reply_token": "reply_token_from_webhook",
  "messages": "Thanks for your message!"
}
```

#### 4. Multicast Messages
Send messages to multiple users at once (max 500 users).

```bash
POST /line.multicast
```

**Example:**
```json
{
  "to": ["U123", "U456", "U789"],
  "messages": "Hello multiple users!"
}
```

### Rich Message Creation

#### Create Text Message
```bash
POST /line.create_text_message
```

```json
{
  "text": "Hello with emojis! 😊",
  "emojis": [
    {
      "index": 18,
      "productId": "5ac1bfd5040ab15980c9b435",
      "emojiId": "001"
    }
  ]
}
```

#### Create Image Message
```bash
POST /line.create_image_message
```

```json
{
  "original_content_url": "https://example.com/image.jpg",
  "preview_image_url": "https://example.com/preview.jpg"
}
```

#### Create Location Message
```bash
POST /line.create_location_message
```

```json
{
  "title": "My Location",
  "address": "123 Main St, City",
  "latitude": 35.6762,
  "longitude": 139.6503
}
```

### User Management

#### Get User Profile
```bash
POST /line.get_profile
```

```json
{
  "user_id": "U1234567890abcdef"
}
```

#### Get Bot Information
```bash
POST /line.get_bot_info
```

```json
{}
```

**Response:**
```json
{
  "userId": "U0eef506b7b32a0eb991299f03d681c03",
  "basicId": "@338mjdbi",
  "displayName": "Your Bot Name",
  "pictureUrl": "https://profile.line-scdn.net/...",
  "chatMode": "chat",
  "markAsReadMode": "manual"
}
```

### Quota Management

#### Get Quota Information
```bash
POST /line.get_quota
```

**Response:**
```json
{
  "type": "limited",
  "value": 15000
}
```

#### Get Quota Consumption
```bash
POST /line.get_quota_consumption
```

**Response:**
```json
{
  "totalUsage": 6037
}
```

### Convenience Methods

#### Send Simple Text
```bash
POST /line.send_text
```

```json
{
  "to": "U1234567890abcdef",
  "text": "Simple text message"
}
```

#### Send Image
```bash
POST /line.send_image
```

```json
{
  "to": "U1234567890abcdef",
  "image_url": "https://example.com/image.jpg",
  "preview_url": "https://example.com/preview.jpg"
}
```

#### Send Sticker
```bash
POST /line.send_sticker
```

```json
{
  "to": "U1234567890abcdef",
  "package_id": "1",
  "sticker_id": "1"
}
```

## Message Types Supported

1. **Text Messages** - Plain text with optional emojis
2. **Image Messages** - JPEG/PNG images up to 10MB
3. **Video Messages** - MP4 videos up to 200MB
4. **Audio Messages** - M4A audio files up to 200MB
5. **Location Messages** - Geographic coordinates with title/address
6. **Sticker Messages** - LINE stickers from available packages

## Error Handling

The service provides comprehensive error handling:

```json
{
  "status": "error",
  "error": "Target ID (to) is required",
  "status_code": 400
}
```

## Rate Limits

- **Broadcast**: 1,000 messages per hour
- **Push/Reply**: 1,000 messages per minute
- **Multicast**: 100 requests per minute

## Security

- All requests require Bearer token authentication
- Webhook signature validation available
- Secure token management through environment variables

## Testing

Use the provided test script to verify functionality:

```bash
python test_line_service.py
```

## Production Deployment

1. Set up your LINE Official Account
2. Get your Channel Access Token
3. Configure the token in `.env`
4. Deploy the API service
5. Set up webhooks (optional)

## Support

For issues or questions about the LINE Messaging API, refer to:
- [LINE Developers Documentation](https://developers.line.biz/en/docs/messaging-api/)
- [LINE Official Account Manager](https://manager.line.biz/)

from fastapi import Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.status import HTTP_403_FORBIDDEN

# Handle both relative and absolute imports
try:
    from .config import get_settings
except ImportError:
    from app.core.config import get_settings

bearer_scheme = HTTPBearer(auto_error=False)

def verify_token(credentials: HTTPAuthorizationCredentials | None = Depends(bearer_scheme)):
    settings = get_settings()
    if not credentials or credentials.credentials != settings.api_token:
        raise HTTPException(HTTP_403_FORBIDDEN, "Invalid or missing token")
    return True

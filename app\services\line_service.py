import httpx
import json
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime

# Handle both relative and absolute imports
try:
    from ..core.config import get_settings
except ImportError:
    from app.core.config import get_settings

logger = logging.getLogger(__name__)

class Line:
    """
    LINE Messaging API service for sending messages, managing users, and handling webhooks.

    Supports:
    - Broadcasting messages to all friends
    - Pushing messages to specific users
    - Replying to messages
    - Multicasting to multiple users
    - Rich message types (text, image, video, audio, location, etc.)
    - User and group management
    """

    def __init__(self, **kwargs):
        """
        Initialize LINE service with authentication token.

        Args:
            token: LINE Channel Access Token (optional, will use from config if not provided)
            **kwargs: Additional parameters
        """
        settings = get_settings()
        self.default_token = kwargs.get("token") or kwargs.get("oauth_token") or settings.line_token
        self.base_url = "https://api.line.me/v2/bot"
        self.timeout = 30

        if not self.default_token:
            logger.warning("LINE token not provided. Some operations may fail.")

    def _get_headers(self, token: Optional[str] = None) -> Dict[str, str]:
        """Get headers for LINE API requests."""
        access_token = token or self.default_token
        return {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }

    async def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None, token: Optional[str] = None) -> Dict[str, Any]:
        """
        Make HTTP request to LINE API.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            data: Request payload
            token: LINE access token

        Returns:
            API response as dictionary
        """
        url = f"{self.base_url}/{endpoint}"
        headers = self._get_headers(token)

        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                if method.upper() == "GET":
                    response = await client.get(url, headers=headers, params=data)
                else:
                    response = await client.request(
                        method.upper(),
                        url,
                        headers=headers,
                        json=data
                    )

                response.raise_for_status()

                # Some endpoints return empty responses
                if response.status_code == 200 and response.text:
                    return response.json()
                else:
                    return {"status": "success", "status_code": response.status_code}

        except httpx.HTTPStatusError as e:
            error_detail = "Unknown error"
            try:
                error_response = e.response.json()
                error_detail = error_response.get("message", str(e))
            except:
                error_detail = str(e)

            logger.error(f"LINE API error: {error_detail}")
            return {
                "status": "error",
                "error": error_detail,
                "status_code": e.response.status_code
            }
        except Exception as e:
            logger.error(f"LINE service error: {str(e)}")
            return {
                "status": "error",
                "error": str(e)
            }

    # ============================================================================
    # MESSAGING METHODS
    # ============================================================================

    async def test_method(self, **kwargs) -> Dict[str, Any]:
        """Simple test method that doesn't make external API calls."""
        return {
            "status": "success",
            "message": "LINE service is working!",
            "test": True,
            "kwargs": kwargs
        }

    async def test_broadcast(self, messages: str = "Test broadcast message", **kwargs) -> Dict[str, Any]:
        """Test broadcast method that simulates a LINE broadcast without making real API calls."""
        # Convert string to message object (same logic as real broadcast)
        if isinstance(messages, str):
            message_objects = [{"type": "text", "text": messages}]
        elif isinstance(messages, dict):
            message_objects = [messages]
        elif isinstance(messages, list):
            message_objects = messages
        else:
            message_objects = [{"type": "text", "text": str(messages)}]

        return {
            "status": "success",
            "message": f"TEST: Broadcast would send {len(message_objects)} message(s) to all friends",
            "message_count": len(message_objects),
            "messages": message_objects,
            "test_mode": True,
            "note": "This is a test method - no actual LINE API call was made",
            "kwargs": kwargs
        }

    async def broadcast(self, messages: Union[str, List[Dict], Dict], token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Send messages to all friends who have added the LINE Official Account.

        Args:
            messages: Message(s) to send. Can be:
                     - String: Simple text message
                     - Dict: Single message object
                     - List[Dict]: Multiple message objects
            token: LINE access token (optional)
            **kwargs: Additional parameters

        Returns:
            API response with status and details

        Example:
            await line.broadcast("Hello everyone!")
            await line.broadcast({"type": "text", "text": "Hello!"})
        """
        # Convert string to message object
        if isinstance(messages, str):
            messages = [{"type": "text", "text": messages}]
        elif isinstance(messages, dict):
            messages = [messages]

        # Validate messages
        if not messages or len(messages) > 5:
            return {
                "status": "error",
                "error": "Must provide 1-5 messages for broadcast"
            }

        payload = {"messages": messages}
        result = await self._make_request("POST", "message/broadcast", payload, token)

        # Enhance the response with more details
        if result.get("status") == "success":
            result["message"] = f"Broadcast sent to all friends with {len(messages)} message(s)"
            result["message_count"] = len(messages)
            result["messages_sent"] = messages
            result["endpoint"] = "message/broadcast"
            result["api_call"] = "LINE Messaging API"
        else:
            # If there's an error, make sure we have a meaningful message
            if not result.get("message") and not result.get("error"):
                result["message"] = "Broadcast request completed but response was empty"
                result["note"] = "This might indicate a successful broadcast with no response body"

        return result

    async def push(self, to: str, messages: Union[str, List[Dict], Dict], token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Send messages to a specific user, group, or room.

        Args:
            to: Target user ID, group ID, or room ID
            messages: Message(s) to send
            token: LINE access token (optional)
            **kwargs: Additional parameters

        Returns:
            API response with status and details

        Example:
            await line.push("U1234567890abcdef", "Hello user!")
        """
        if not to:
            return {"status": "error", "error": "Target ID (to) is required"}

        # Convert string to message object
        if isinstance(messages, str):
            messages = [{"type": "text", "text": messages}]
        elif isinstance(messages, dict):
            messages = [messages]

        # Validate messages
        if not messages or len(messages) > 5:
            return {
                "status": "error",
                "error": "Must provide 1-5 messages for push"
            }

        payload = {
            "to": to,
            "messages": messages
        }

        result = await self._make_request("POST", "message/push", payload, token)

        if result.get("status") == "success":
            result["message"] = f"Message pushed to {to} with {len(messages)} message(s)"
            result["target"] = to
            result["message_count"] = len(messages)

        return result

    async def reply(self, reply_token: str, messages: Union[str, List[Dict], Dict], token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Reply to a user message using reply token.

        Args:
            reply_token: Reply token from webhook event
            messages: Message(s) to send
            token: LINE access token (optional)
            **kwargs: Additional parameters

        Returns:
            API response with status and details

        Example:
            await line.reply("reply_token_123", "Thanks for your message!")
        """
        if not reply_token:
            return {"status": "error", "error": "Reply token is required"}

        # Convert string to message object
        if isinstance(messages, str):
            messages = [{"type": "text", "text": messages}]
        elif isinstance(messages, dict):
            messages = [messages]

        # Validate messages
        if not messages or len(messages) > 5:
            return {
                "status": "error",
                "error": "Must provide 1-5 messages for reply"
            }

        payload = {
            "replyToken": reply_token,
            "messages": messages
        }

        result = await self._make_request("POST", "message/reply", payload, token)

        if result.get("status") == "success":
            result["message"] = f"Reply sent with {len(messages)} message(s)"
            result["reply_token"] = reply_token
            result["message_count"] = len(messages)

        return result

    async def multicast(self, to: List[str], messages: Union[str, List[Dict], Dict], token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Send messages to multiple users.

        Args:
            to: List of user IDs (max 500)
            messages: Message(s) to send
            token: LINE access token (optional)
            **kwargs: Additional parameters

        Returns:
            API response with status and details

        Example:
            await line.multicast(["U123", "U456"], "Hello users!")
        """
        if not to or not isinstance(to, list):
            return {"status": "error", "error": "Target list (to) is required and must be a list"}

        if len(to) > 500:
            return {"status": "error", "error": "Cannot send to more than 500 users at once"}

        # Convert string to message object
        if isinstance(messages, str):
            messages = [{"type": "text", "text": messages}]
        elif isinstance(messages, dict):
            messages = [messages]

        # Validate messages
        if not messages or len(messages) > 5:
            return {
                "status": "error",
                "error": "Must provide 1-5 messages for multicast"
            }

        payload = {
            "to": to,
            "messages": messages
        }

        result = await self._make_request("POST", "message/multicast", payload, token)

        if result.get("status") == "success":
            result["message"] = f"Multicast sent to {len(to)} users with {len(messages)} message(s)"
            result["target_count"] = len(to)
            result["message_count"] = len(messages)

        return result

    # ============================================================================
    # RICH MESSAGE HELPERS
    # ============================================================================

    def create_text_message(self, text: str, emojis: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """
        Create a text message object.

        Args:
            text: Message text (max 5000 characters)
            emojis: List of emoji objects (optional)

        Returns:
            Text message object
        """
        message = {
            "type": "text",
            "text": text[:5000]  # Limit to 5000 characters
        }

        if emojis:
            message["emojis"] = emojis

        return message

    def create_image_message(self, original_content_url: str, preview_image_url: str) -> Dict[str, Any]:
        """
        Create an image message object.

        Args:
            original_content_url: URL of the original image (HTTPS, JPEG/PNG, max 10MB)
            preview_image_url: URL of the preview image (HTTPS, JPEG/PNG, max 1MB)

        Returns:
            Image message object
        """
        return {
            "type": "image",
            "originalContentUrl": original_content_url,
            "previewImageUrl": preview_image_url
        }

    def create_video_message(self, original_content_url: str, preview_image_url: str) -> Dict[str, Any]:
        """
        Create a video message object.

        Args:
            original_content_url: URL of the video file (HTTPS, MP4, max 200MB)
            preview_image_url: URL of the preview image (HTTPS, JPEG/PNG, max 1MB)

        Returns:
            Video message object
        """
        return {
            "type": "video",
            "originalContentUrl": original_content_url,
            "previewImageUrl": preview_image_url
        }

    def create_audio_message(self, original_content_url: str, duration: int) -> Dict[str, Any]:
        """
        Create an audio message object.

        Args:
            original_content_url: URL of the audio file (HTTPS, M4A, max 200MB)
            duration: Length of audio file in milliseconds

        Returns:
            Audio message object
        """
        return {
            "type": "audio",
            "originalContentUrl": original_content_url,
            "duration": duration
        }

    def create_location_message(self, title: str, address: str, latitude: float, longitude: float) -> Dict[str, Any]:
        """
        Create a location message object.

        Args:
            title: Title of the location
            address: Address of the location
            latitude: Latitude coordinate
            longitude: Longitude coordinate

        Returns:
            Location message object
        """
        return {
            "type": "location",
            "title": title,
            "address": address,
            "latitude": latitude,
            "longitude": longitude
        }

    def create_sticker_message(self, package_id: str, sticker_id: str) -> Dict[str, Any]:
        """
        Create a sticker message object.

        Args:
            package_id: Package ID of the sticker
            sticker_id: Sticker ID

        Returns:
            Sticker message object
        """
        return {
            "type": "sticker",
            "packageId": package_id,
            "stickerId": sticker_id
        }

    # ============================================================================
    # USER MANAGEMENT METHODS
    # ============================================================================

    async def get_profile(self, user_id: str, token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Get user profile information.

        Args:
            user_id: User ID
            token: LINE access token (optional)
            **kwargs: Additional parameters

        Returns:
            User profile information

        Example:
            await line.get_profile("U1234567890abcdef")
        """
        if not user_id:
            return {"status": "error", "error": "User ID is required"}

        result = await self._make_request("GET", f"profile/{user_id}", None, token)

        if result.get("status") == "success":
            result["message"] = f"Profile retrieved for user {user_id}"

        return result

    async def get_group_member_profile(self, group_id: str, user_id: str, token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Get group member profile information.

        Args:
            group_id: Group ID
            user_id: User ID
            token: LINE access token (optional)
            **kwargs: Additional parameters

        Returns:
            Group member profile information
        """
        if not group_id or not user_id:
            return {"status": "error", "error": "Group ID and User ID are required"}

        result = await self._make_request("GET", f"group/{group_id}/member/{user_id}", None, token)

        if result.get("status") == "success":
            result["message"] = f"Group member profile retrieved for user {user_id} in group {group_id}"

        return result

    async def get_room_member_profile(self, room_id: str, user_id: str, token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Get room member profile information.

        Args:
            room_id: Room ID
            user_id: User ID
            token: LINE access token (optional)
            **kwargs: Additional parameters

        Returns:
            Room member profile information
        """
        if not room_id or not user_id:
            return {"status": "error", "error": "Room ID and User ID are required"}

        result = await self._make_request("GET", f"room/{room_id}/member/{user_id}", None, token)

        if result.get("status") == "success":
            result["message"] = f"Room member profile retrieved for user {user_id} in room {room_id}"

        return result

    async def leave_group(self, group_id: str, token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Leave a group.

        Args:
            group_id: Group ID to leave
            token: LINE access token (optional)
            **kwargs: Additional parameters

        Returns:
            API response
        """
        if not group_id:
            return {"status": "error", "error": "Group ID is required"}

        result = await self._make_request("POST", f"group/{group_id}/leave", {}, token)

        if result.get("status") == "success":
            result["message"] = f"Left group {group_id}"

        return result

    async def leave_room(self, room_id: str, token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Leave a room.

        Args:
            room_id: Room ID to leave
            token: LINE access token (optional)
            **kwargs: Additional parameters

        Returns:
            API response
        """
        if not room_id:
            return {"status": "error", "error": "Room ID is required"}

        result = await self._make_request("POST", f"room/{room_id}/leave", {}, token)

        if result.get("status") == "success":
            result["message"] = f"Left room {room_id}"

        return result

    # ============================================================================
    # UTILITY METHODS
    # ============================================================================

    async def get_bot_info(self, token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Get bot information.

        Args:
            token: LINE access token (optional)
            **kwargs: Additional parameters

        Returns:
            Bot information
        """
        result = await self._make_request("GET", "info", None, token)

        if result.get("status") == "success":
            result["message"] = "Bot information retrieved successfully"

        return result

    async def get_quota(self, token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Get the current quota consumption for this month.

        Args:
            token: LINE access token (optional)
            **kwargs: Additional parameters

        Returns:
            Quota information
        """
        result = await self._make_request("GET", "message/quota", None, token)

        if result.get("status") == "success":
            result["message"] = "Quota information retrieved successfully"

        return result

    async def get_quota_consumption(self, token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Get the number of messages sent this month.

        Args:
            token: LINE access token (optional)
            **kwargs: Additional parameters

        Returns:
            Quota consumption information
        """
        result = await self._make_request("GET", "message/quota/consumption", None, token)

        if result.get("status") == "success":
            result["message"] = "Quota consumption retrieved successfully"

        return result

    def validate_webhook_signature(self, body: str, signature: str, channel_secret: str) -> bool:
        """
        Validate LINE webhook signature.

        Args:
            body: Request body as string
            signature: X-Line-Signature header value
            channel_secret: LINE channel secret

        Returns:
            True if signature is valid, False otherwise
        """
        import hmac
        import hashlib
        import base64

        hash_digest = hmac.new(
            channel_secret.encode('utf-8'),
            body.encode('utf-8'),
            hashlib.sha256
        ).digest()

        expected_signature = base64.b64encode(hash_digest).decode('utf-8')
        return hmac.compare_digest(signature, expected_signature)

    # ============================================================================
    # CONVENIENCE METHODS
    # ============================================================================

    async def send_text(self, to: str, text: str, token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Convenience method to send a simple text message.

        Args:
            to: Target user ID, group ID, or room ID
            text: Text message to send
            token: LINE access token (optional)
            **kwargs: Additional parameters

        Returns:
            API response
        """
        return await self.push(to, text, token, **kwargs)

    async def send_image(self, to: str, image_url: str, preview_url: Optional[str] = None, token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Convenience method to send an image message.

        Args:
            to: Target user ID, group ID, or room ID
            image_url: URL of the image
            preview_url: URL of the preview image (defaults to image_url)
            token: LINE access token (optional)
            **kwargs: Additional parameters

        Returns:
            API response
        """
        preview_url = preview_url or image_url
        image_message = self.create_image_message(image_url, preview_url)
        return await self.push(to, image_message, token, **kwargs)

    async def send_sticker(self, to: str, package_id: str, sticker_id: str, token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Convenience method to send a sticker message.

        Args:
            to: Target user ID, group ID, or room ID
            package_id: Sticker package ID
            sticker_id: Sticker ID
            token: LINE access token (optional)
            **kwargs: Additional parameters

        Returns:
            API response
        """
        sticker_message = self.create_sticker_message(package_id, sticker_id)
        return await self.push(to, sticker_message, token, **kwargs)

    async def send_location(self, to: str, title: str, address: str, latitude: float, longitude: float, token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Convenience method to send a location message.

        Args:
            to: Target user ID, group ID, or room ID
            title: Location title
            address: Location address
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            token: LINE access token (optional)
            **kwargs: Additional parameters

        Returns:
            API response
        """
        location_message = self.create_location_message(title, address, latitude, longitude)
        return await self.push(to, location_message, token, **kwargs)

    # ============================================================================
    # BATCH OPERATIONS
    # ============================================================================

    async def send_multiple_messages(self, targets: List[Dict[str, Any]], token: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """
        Send different messages to multiple targets efficiently.

        Args:
            targets: List of target objects with 'to' and 'messages' keys
            token: LINE access token (optional)
            **kwargs: Additional parameters

        Example:
            targets = [
                {"to": "U123", "messages": "Hello User 1"},
                {"to": "U456", "messages": "Hello User 2"}
            ]

        Returns:
            Batch operation results
        """
        results = []

        for target in targets:
            if not isinstance(target, dict) or 'to' not in target or 'messages' not in target:
                results.append({
                    "target": target,
                    "status": "error",
                    "error": "Invalid target format. Must have 'to' and 'messages' keys"
                })
                continue

            result = await self.push(target['to'], target['messages'], token)
            result['target'] = target['to']
            results.append(result)

        success_count = sum(1 for r in results if r.get('status') == 'success')

        return {
            "status": "completed",
            "total_targets": len(targets),
            "successful": success_count,
            "failed": len(targets) - success_count,
            "results": results
        }



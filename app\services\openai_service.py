"""
OpenAI Service for Common Class API

A comprehensive service for interacting with OpenAI's API including:
- Chat Completions (GPT models)
- Image Generation (DALL-E)
- Text-to-Speech (TTS)
- Speech-to-Text (Whisper)
- Embeddings
- Fine-tuning
- Assistants API
- Vision capabilities

Author: Common Class API
Version: 1.0.0
"""

import asyncio
import base64
import io
import logging
from typing import Dict, List, Any, Optional, Union
import httpx
from openai import AsyncOpenAI
from app.core.config import get_settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Openai:
    """
    OpenAI service class providing comprehensive access to OpenAI's API.

    This service supports all major OpenAI features including chat completions,
    image generation, speech processing, embeddings, and the Assistants API.
    """

    def __init__(self, **kwargs):
        """
        Initialize OpenAI service.

        Args:
            token: OpenAI API key
            base_url: Custom base URL (optional)
            organization: OpenAI organization ID (optional)
            project: OpenAI project ID (optional)
            **kwargs: Additional parameters
        """
        settings = get_settings()
        self.default_token = kwargs.get("token") or kwargs.get("api_key") or kwargs.get("openai_token") or settings.openai_token
        self.base_url = kwargs.get("base_url", "https://api.openai.com/v1")
        self.organization = kwargs.get("organization")
        self.project = kwargs.get("project")

        # Default model configurations
        self.default_chat_model = kwargs.get("chat_model", "gpt-4o-mini")
        self.default_image_model = kwargs.get("image_model", "dall-e-3")
        self.default_tts_model = kwargs.get("tts_model", "tts-1")
        self.default_whisper_model = kwargs.get("whisper_model", "whisper-1")
        self.default_embedding_model = kwargs.get("embedding_model", "text-embedding-3-small")

        if not self.default_token:
            logger.warning("OpenAI token not provided. Some operations may fail.")

    def _get_client(self, token: Optional[str] = None) -> AsyncOpenAI:
        """Get configured OpenAI client."""
        api_key = token or self.default_token
        if not api_key:
            raise ValueError("OpenAI API key is required")

        return AsyncOpenAI(
            api_key=api_key,
            base_url=self.base_url,
            organization=self.organization,
            project=self.project
        )

    # ============================================================================
    # TEST METHODS
    # ============================================================================

    async def test_method(self, **kwargs) -> Dict[str, Any]:
        """Simple test method that doesn't make external API calls."""
        return {
            "status": "success",
            "message": "OpenAI service is working!",
            "test": True,
            "default_models": {
                "chat": self.default_chat_model,
                "image": self.default_image_model,
                "tts": self.default_tts_model,
                "whisper": self.default_whisper_model,
                "embedding": self.default_embedding_model
            },
            "kwargs": kwargs
        }

    async def test_chat(self, message: str = "Hello, how are you?", **kwargs) -> Dict[str, Any]:
        """Test chat method that simulates an OpenAI chat completion."""
        return {
            "status": "success",
            "message": "TEST: Chat completion simulation",
            "test_mode": True,
            "input": {
                "message": message,
                "model": kwargs.get("model", self.default_chat_model),
                "max_tokens": kwargs.get("max_tokens", 150),
                "temperature": kwargs.get("temperature", 0.7)
            },
            "simulated_response": {
                "id": "chatcmpl-test123",
                "object": "chat.completion",
                "model": kwargs.get("model", self.default_chat_model),
                "choices": [{
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": f"TEST RESPONSE: I received your message '{message}'. This is a simulated response."
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": 10,
                    "completion_tokens": 15,
                    "total_tokens": 25
                }
            },
            "note": "This is a test method - no actual OpenAI API call was made",
            "kwargs": kwargs
        }

    # ============================================================================
    # CHAT COMPLETIONS
    # ============================================================================

    async def chat(
        self,
        message: Union[str, List[Dict[str, str]]],
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        system_prompt: Optional[str] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a chat completion.

        Args:
            message: User message (string) or conversation history (list of messages)
            model: Model to use (default: gpt-4o-mini)
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature (0-2)
            system_prompt: System message to set behavior
            token: OpenAI API key
            **kwargs: Additional OpenAI parameters

        Returns:
            Chat completion response

        Example:
            await openai.chat("Hello, how are you?")
            await openai.chat("Explain quantum physics", model="gpt-4", max_tokens=500)
        """
        try:
            client = self._get_client(token)

            # Prepare messages
            if isinstance(message, str):
                messages = []
                if system_prompt:
                    messages.append({"role": "system", "content": system_prompt})
                messages.append({"role": "user", "content": message})
            else:
                messages = message

            # Prepare parameters
            params = {
                "model": model or self.default_chat_model,
                "messages": messages,
                **kwargs
            }

            if max_tokens is not None:
                params["max_tokens"] = max_tokens
            if temperature is not None:
                params["temperature"] = temperature

            # Make API call
            response = await client.chat.completions.create(**params)

            return {
                "status": "success",
                "message": "Chat completion generated successfully",
                "response": response.model_dump(),
                "content": response.choices[0].message.content if response.choices else None,
                "model_used": response.model,
                "usage": response.usage.model_dump() if response.usage else None
            }

        except Exception as e:
            logger.error(f"OpenAI chat error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to generate chat completion"
            }

    # Legacy method for backward compatibility
    async def request(
        self,
        message: str,
        assistant_id: Optional[str] = None,
        image_url: Optional[str] = None,
        thread_id: Optional[str] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Legacy request method for backward compatibility.
        Redirects to appropriate method based on parameters.
        """
        if assistant_id:
            return await self.assistant_message(
                message=message,
                assistant_id=assistant_id,
                thread_id=thread_id,
                token=token,
                **kwargs
            )
        elif image_url:
            return await self.vision(
                message=message,
                image_url=image_url,
                token=token,
                **kwargs
            )
        else:
            return await self.chat(
                message=message,
                token=token,
                **kwargs
            )

    # ============================================================================
    # VISION (GPT-4 Vision)
    # ============================================================================

    async def vision(
        self,
        message: str,
        image_url: Optional[str] = None,
        image_base64: Optional[str] = None,
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Analyze images with GPT-4 Vision.

        Args:
            message: Text prompt/question about the image
            image_url: URL of the image to analyze
            image_base64: Base64 encoded image data
            model: Model to use (default: gpt-4o-mini)
            max_tokens: Maximum tokens to generate
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Vision analysis response

        Example:
            await openai.vision("What's in this image?", image_url="https://example.com/image.jpg")
        """
        try:
            if not image_url and not image_base64:
                return {
                    "status": "error",
                    "error": "Either image_url or image_base64 is required"
                }

            client = self._get_client(token)

            # Prepare image content
            if image_base64:
                image_content = f"data:image/jpeg;base64,{image_base64}"
            else:
                image_content = image_url

            messages = [{
                "role": "user",
                "content": [
                    {"type": "text", "text": message},
                    {"type": "image_url", "image_url": {"url": image_content}}
                ]
            }]

            params = {
                "model": model or "gpt-4o-mini",
                "messages": messages,
                "max_tokens": max_tokens or 300,
                **kwargs
            }

            response = await client.chat.completions.create(**params)

            return {
                "status": "success",
                "message": "Vision analysis completed successfully",
                "response": response.model_dump(),
                "content": response.choices[0].message.content if response.choices else None,
                "model_used": response.model,
                "usage": response.usage.model_dump() if response.usage else None
            }

        except Exception as e:
            logger.error(f"OpenAI vision error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to analyze image"
            }

    # ============================================================================
    # IMAGE GENERATION (DALL-E)
    # ============================================================================

    async def generate_image(
        self,
        prompt: str,
        model: Optional[str] = None,
        size: Optional[str] = None,
        quality: Optional[str] = None,
        n: Optional[int] = None,
        response_format: Optional[str] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate images with DALL-E.

        Args:
            prompt: Text description of the desired image
            model: Model to use (dall-e-2, dall-e-3)
            size: Image size (256x256, 512x512, 1024x1024, 1792x1024, 1024x1792)
            quality: Image quality (standard, hd) - DALL-E 3 only
            n: Number of images to generate (1-10 for DALL-E 2, 1 for DALL-E 3)
            response_format: Response format (url, b64_json)
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Image generation response

        Example:
            await openai.generate_image("A futuristic city at sunset")
        """
        try:
            client = self._get_client(token)

            params = {
                "prompt": prompt,
                "model": model or self.default_image_model,
                **kwargs
            }

            # Set defaults based on model
            if model == "dall-e-2":
                params.update({
                    "size": size or "1024x1024",
                    "n": n or 1
                })
            else:  # dall-e-3
                params.update({
                    "size": size or "1024x1024",
                    "quality": quality or "standard",
                    "n": 1  # DALL-E 3 only supports n=1
                })

            if response_format:
                params["response_format"] = response_format

            response = await client.images.generate(**params)

            return {
                "status": "success",
                "message": f"Generated {len(response.data)} image(s) successfully",
                "response": response.model_dump(),
                "images": [img.model_dump() for img in response.data],
                "model_used": params["model"],
                "prompt": prompt
            }

        except Exception as e:
            logger.error(f"OpenAI image generation error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to generate image"
            }

    async def edit_image(
        self,
        image: str,  # File path or base64
        prompt: str,
        mask: Optional[str] = None,
        model: Optional[str] = None,
        n: Optional[int] = None,
        size: Optional[str] = None,
        response_format: Optional[str] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Edit images with DALL-E 2.

        Args:
            image: Image to edit (file path or base64)
            prompt: Text description of the desired edit
            mask: Mask image (optional)
            model: Model to use (dall-e-2)
            n: Number of images to generate (1-10)
            size: Image size (256x256, 512x512, 1024x1024)
            response_format: Response format (url, b64_json)
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Image edit response
        """
        try:
            client = self._get_client(token)

            # Note: This is a simplified version
            # In a real implementation, you'd handle file uploads properly
            return {
                "status": "error",
                "error": "Image editing requires file upload handling",
                "message": "This feature needs additional implementation for file handling"
            }

        except Exception as e:
            logger.error(f"OpenAI image edit error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to edit image"
            }

    async def create_image_variation(
        self,
        image: str,  # File path or base64
        model: Optional[str] = None,
        n: Optional[int] = None,
        size: Optional[str] = None,
        response_format: Optional[str] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create variations of an image with DALL-E 2.

        Args:
            image: Image to create variations of
            model: Model to use (dall-e-2)
            n: Number of variations to generate (1-10)
            size: Image size (256x256, 512x512, 1024x1024)
            response_format: Response format (url, b64_json)
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Image variation response
        """
        try:
            client = self._get_client(token)

            # Note: This is a simplified version
            # In a real implementation, you'd handle file uploads properly
            return {
                "status": "error",
                "error": "Image variation requires file upload handling",
                "message": "This feature needs additional implementation for file handling"
            }

        except Exception as e:
            logger.error(f"OpenAI image variation error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to create image variation"
            }

    # ============================================================================
    # TEXT-TO-SPEECH (TTS)
    # ============================================================================

    async def text_to_speech(
        self,
        text: str,
        model: Optional[str] = None,
        voice: Optional[str] = None,
        response_format: Optional[str] = None,
        speed: Optional[float] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Convert text to speech using OpenAI's TTS models.

        Args:
            text: Text to convert to speech (max 4096 characters)
            model: TTS model to use (tts-1, tts-1-hd)
            voice: Voice to use (alloy, echo, fable, onyx, nova, shimmer)
            response_format: Audio format (mp3, opus, aac, flac, wav, pcm)
            speed: Speed of speech (0.25 to 4.0)
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            TTS response with audio data

        Example:
            await openai.text_to_speech("Hello, world!", voice="alloy")
        """
        try:
            client = self._get_client(token)

            params = {
                "model": model or self.default_tts_model,
                "input": text,
                "voice": voice or "alloy",
                **kwargs
            }

            if response_format:
                params["response_format"] = response_format
            if speed is not None:
                params["speed"] = speed

            response = await client.audio.speech.create(**params)

            # Convert audio response to base64 for JSON serialization
            audio_content = response.content
            audio_base64 = base64.b64encode(audio_content).decode('utf-8')

            return {
                "status": "success",
                "message": "Text-to-speech conversion completed successfully",
                "audio_base64": audio_base64,
                "audio_format": params.get("response_format", "mp3"),
                "model_used": params["model"],
                "voice_used": params["voice"],
                "text_length": len(text),
                "audio_size_bytes": len(audio_content)
            }

        except Exception as e:
            logger.error(f"OpenAI TTS error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to convert text to speech"
            }

    # ============================================================================
    # SPEECH-TO-TEXT (WHISPER)
    # ============================================================================

    async def speech_to_text(
        self,
        audio_file: str,  # Base64 encoded audio or file path
        model: Optional[str] = None,
        language: Optional[str] = None,
        prompt: Optional[str] = None,
        response_format: Optional[str] = None,
        temperature: Optional[float] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Transcribe audio to text using Whisper.

        Args:
            audio_file: Base64 encoded audio data or file path
            model: Whisper model to use (whisper-1)
            language: Language of the audio (ISO-639-1 format)
            prompt: Optional text to guide the model's style
            response_format: Response format (json, text, srt, verbose_json, vtt)
            temperature: Sampling temperature (0 to 1)
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Transcription response

        Example:
            await openai.speech_to_text(audio_base64, language="en")
        """
        try:
            client = self._get_client(token)

            # For now, return a placeholder since file handling needs proper implementation
            return {
                "status": "error",
                "error": "Speech-to-text requires proper file upload handling",
                "message": "This feature needs additional implementation for audio file handling",
                "note": "Would transcribe audio using Whisper model",
                "parameters": {
                    "model": model or self.default_whisper_model,
                    "language": language,
                    "prompt": prompt,
                    "response_format": response_format or "json",
                    "temperature": temperature
                }
            }

        except Exception as e:
            logger.error(f"OpenAI Whisper error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to transcribe audio"
            }

    async def translate_audio(
        self,
        audio_file: str,  # Base64 encoded audio or file path
        model: Optional[str] = None,
        prompt: Optional[str] = None,
        response_format: Optional[str] = None,
        temperature: Optional[float] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Translate audio to English text using Whisper.

        Args:
            audio_file: Base64 encoded audio data or file path
            model: Whisper model to use (whisper-1)
            prompt: Optional text to guide the model's style
            response_format: Response format (json, text, srt, verbose_json, vtt)
            temperature: Sampling temperature (0 to 1)
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Translation response

        Example:
            await openai.translate_audio(audio_base64)
        """
        try:
            client = self._get_client(token)

            # For now, return a placeholder since file handling needs proper implementation
            return {
                "status": "error",
                "error": "Audio translation requires proper file upload handling",
                "message": "This feature needs additional implementation for audio file handling",
                "note": "Would translate audio to English using Whisper model",
                "parameters": {
                    "model": model or self.default_whisper_model,
                    "prompt": prompt,
                    "response_format": response_format or "json",
                    "temperature": temperature
                }
            }

        except Exception as e:
            logger.error(f"OpenAI audio translation error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to translate audio"
            }

    # ============================================================================
    # EMBEDDINGS
    # ============================================================================

    async def create_embeddings(
        self,
        input_text: Union[str, List[str]],
        model: Optional[str] = None,
        encoding_format: Optional[str] = None,
        dimensions: Optional[int] = None,
        user: Optional[str] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create embeddings for text input.

        Args:
            input_text: Text or list of texts to embed
            model: Embedding model to use (text-embedding-3-small, text-embedding-3-large, text-embedding-ada-002)
            encoding_format: Format for embeddings (float, base64)
            dimensions: Number of dimensions for embedding (only for text-embedding-3 models)
            user: Unique identifier for the end-user
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Embeddings response

        Example:
            await openai.create_embeddings("Hello world")
            await openai.create_embeddings(["Hello", "World"], model="text-embedding-3-large")
        """
        try:
            client = self._get_client(token)

            params = {
                "model": model or self.default_embedding_model,
                "input": input_text,
                **kwargs
            }

            if encoding_format:
                params["encoding_format"] = encoding_format
            if dimensions is not None:
                params["dimensions"] = dimensions
            if user:
                params["user"] = user

            response = await client.embeddings.create(**params)

            return {
                "status": "success",
                "message": f"Created embeddings for {len(response.data)} input(s)",
                "response": response.model_dump(),
                "embeddings": [embedding.embedding for embedding in response.data],
                "model_used": response.model,
                "usage": response.usage.model_dump() if response.usage else None,
                "dimensions": len(response.data[0].embedding) if response.data else 0
            }

        except Exception as e:
            logger.error(f"OpenAI embeddings error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to create embeddings"
            }

    # ============================================================================
    # MODERATION
    # ============================================================================

    async def moderate_content(
        self,
        input_text: Union[str, List[str]],
        model: Optional[str] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Check if content violates OpenAI's usage policies.

        Args:
            input_text: Text or list of texts to moderate
            model: Moderation model to use (text-moderation-latest, text-moderation-stable)
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Moderation response

        Example:
            await openai.moderate_content("This is a test message")
        """
        try:
            client = self._get_client(token)

            params = {
                "input": input_text,
                **kwargs
            }

            if model:
                params["model"] = model

            response = await client.moderations.create(**params)

            return {
                "status": "success",
                "message": f"Moderated {len(response.results)} input(s)",
                "response": response.model_dump(),
                "results": [result.model_dump() for result in response.results],
                "flagged": any(result.flagged for result in response.results),
                "model_used": response.model
            }

        except Exception as e:
            logger.error(f"OpenAI moderation error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to moderate content"
            }

    # ============================================================================
    # MODELS
    # ============================================================================

    async def list_models(
        self,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        List available OpenAI models.

        Args:
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Models list response

        Example:
            await openai.list_models()
        """
        try:
            client = self._get_client(token)

            response = await client.models.list(**kwargs)

            return {
                "status": "success",
                "message": f"Retrieved {len(response.data)} models",
                "response": response.model_dump(),
                "models": [model.model_dump() for model in response.data],
                "model_count": len(response.data)
            }

        except Exception as e:
            logger.error(f"OpenAI models list error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to list models"
            }

    async def get_model(
        self,
        model_id: str,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Get details about a specific model.

        Args:
            model_id: ID of the model to retrieve
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Model details response

        Example:
            await openai.get_model("gpt-4o-mini")
        """
        try:
            client = self._get_client(token)

            response = await client.models.retrieve(model_id, **kwargs)

            return {
                "status": "success",
                "message": f"Retrieved model details for {model_id}",
                "response": response.model_dump(),
                "model": response.model_dump()
            }

        except Exception as e:
            logger.error(f"OpenAI model retrieve error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": f"Failed to retrieve model {model_id}"
            }

    # ============================================================================
    # ASSISTANTS API
    # ============================================================================

    async def create_assistant(
        self,
        model: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        instructions: Optional[str] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        file_ids: Optional[List[str]] = None,
        metadata: Optional[Dict[str, str]] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create an OpenAI Assistant.

        Args:
            model: Model to use for the assistant
            name: Name of the assistant
            description: Description of the assistant
            instructions: System instructions for the assistant
            tools: List of tools enabled for the assistant
            file_ids: List of file IDs attached to the assistant
            metadata: Metadata for the assistant
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Assistant creation response

        Example:
            await openai.create_assistant(
                model="gpt-4o-mini",
                name="Math Tutor",
                instructions="You are a helpful math tutor."
            )
        """
        try:
            client = self._get_client(token)

            params = {
                "model": model,
                **kwargs
            }

            if name:
                params["name"] = name
            if description:
                params["description"] = description
            if instructions:
                params["instructions"] = instructions
            if tools:
                params["tools"] = tools
            if file_ids:
                params["file_ids"] = file_ids
            if metadata:
                params["metadata"] = metadata

            response = await client.beta.assistants.create(**params)

            return {
                "status": "success",
                "message": f"Created assistant '{response.name or response.id}'",
                "response": response.model_dump(),
                "assistant": response.model_dump(),
                "assistant_id": response.id
            }

        except Exception as e:
            logger.error(f"OpenAI assistant creation error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to create assistant"
            }

    async def list_assistants(
        self,
        limit: Optional[int] = None,
        order: Optional[str] = None,
        after: Optional[str] = None,
        before: Optional[str] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        List OpenAI Assistants.

        Args:
            limit: Number of assistants to retrieve (max 100)
            order: Sort order (asc, desc)
            after: Cursor for pagination
            before: Cursor for pagination
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Assistants list response

        Example:
            await openai.list_assistants(limit=10)
        """
        try:
            client = self._get_client(token)

            params = {**kwargs}

            if limit is not None:
                params["limit"] = limit
            if order:
                params["order"] = order
            if after:
                params["after"] = after
            if before:
                params["before"] = before

            response = await client.beta.assistants.list(**params)

            return {
                "status": "success",
                "message": f"Retrieved {len(response.data)} assistants",
                "response": response.model_dump(),
                "assistants": [assistant.model_dump() for assistant in response.data],
                "count": len(response.data)
            }

        except Exception as e:
            logger.error(f"OpenAI assistants list error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to list assistants"
            }

    async def get_assistant(
        self,
        assistant_id: str,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Get details about a specific assistant.

        Args:
            assistant_id: ID of the assistant to retrieve
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Assistant details response

        Example:
            await openai.get_assistant("asst_abc123")
        """
        try:
            client = self._get_client(token)

            response = await client.beta.assistants.retrieve(assistant_id, **kwargs)

            return {
                "status": "success",
                "message": f"Retrieved assistant {assistant_id}",
                "response": response.model_dump(),
                "assistant": response.model_dump()
            }

        except Exception as e:
            logger.error(f"OpenAI assistant retrieve error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": f"Failed to retrieve assistant {assistant_id}"
            }

    async def create_thread(
        self,
        messages: Optional[List[Dict[str, Any]]] = None,
        metadata: Optional[Dict[str, str]] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a conversation thread.

        Args:
            messages: Initial messages for the thread
            metadata: Metadata for the thread
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Thread creation response

        Example:
            await openai.create_thread()
            await openai.create_thread(messages=[{"role": "user", "content": "Hello!"}])
        """
        try:
            client = self._get_client(token)

            params = {**kwargs}

            if messages:
                params["messages"] = messages
            if metadata:
                params["metadata"] = metadata

            response = await client.beta.threads.create(**params)

            return {
                "status": "success",
                "message": f"Created thread {response.id}",
                "response": response.model_dump(),
                "thread": response.model_dump(),
                "thread_id": response.id
            }

        except Exception as e:
            logger.error(f"OpenAI thread creation error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to create thread"
            }

    async def add_message_to_thread(
        self,
        thread_id: str,
        content: str,
        role: str = "user",
        file_ids: Optional[List[str]] = None,
        metadata: Optional[Dict[str, str]] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Add a message to a thread.

        Args:
            thread_id: ID of the thread
            content: Content of the message
            role: Role of the message sender (user, assistant)
            file_ids: List of file IDs attached to the message
            metadata: Metadata for the message
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Message creation response

        Example:
            await openai.add_message_to_thread("thread_abc123", "Hello!")
        """
        try:
            client = self._get_client(token)

            params = {
                "role": role,
                "content": content,
                **kwargs
            }

            if file_ids:
                params["file_ids"] = file_ids
            if metadata:
                params["metadata"] = metadata

            response = await client.beta.threads.messages.create(
                thread_id=thread_id,
                **params
            )

            return {
                "status": "success",
                "message": f"Added message to thread {thread_id}",
                "response": response.model_dump(),
                "message_data": response.model_dump(),
                "message_id": response.id
            }

        except Exception as e:
            logger.error(f"OpenAI add message error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to add message to thread"
            }

    async def run_assistant(
        self,
        thread_id: str,
        assistant_id: str,
        instructions: Optional[str] = None,
        additional_instructions: Optional[str] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        metadata: Optional[Dict[str, str]] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Run an assistant on a thread.

        Args:
            thread_id: ID of the thread
            assistant_id: ID of the assistant
            instructions: Override instructions for this run
            additional_instructions: Additional instructions for this run
            tools: Override tools for this run
            metadata: Metadata for the run
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Run creation response

        Example:
            await openai.run_assistant("thread_abc123", "asst_def456")
        """
        try:
            client = self._get_client(token)

            params = {
                "assistant_id": assistant_id,
                **kwargs
            }

            if instructions:
                params["instructions"] = instructions
            if additional_instructions:
                params["additional_instructions"] = additional_instructions
            if tools:
                params["tools"] = tools
            if metadata:
                params["metadata"] = metadata

            response = await client.beta.threads.runs.create(
                thread_id=thread_id,
                **params
            )

            return {
                "status": "success",
                "message": f"Started run {response.id} on thread {thread_id}",
                "response": response.model_dump(),
                "run": response.model_dump(),
                "run_id": response.id,
                "run_status": response.status
            }

        except Exception as e:
            logger.error(f"OpenAI run assistant error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to run assistant"
            }

    async def assistant_message(
        self,
        message: str,
        assistant_id: str,
        thread_id: Optional[str] = None,
        instructions: Optional[str] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Send a message to an assistant and get a response.
        This is a convenience method that handles the full conversation flow.

        Args:
            message: Message to send to the assistant
            assistant_id: ID of the assistant
            thread_id: ID of existing thread (optional, will create new if not provided)
            instructions: Additional instructions for this interaction
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Assistant response

        Example:
            await openai.assistant_message("Hello!", "asst_abc123")
        """
        try:
            client = self._get_client(token)

            # Create thread if not provided
            if not thread_id:
                thread_response = await self.create_thread(token=token)
                if thread_response["status"] != "success":
                    return thread_response
                thread_id = thread_response["thread_id"]

            # Add message to thread
            message_response = await self.add_message_to_thread(
                thread_id=thread_id,
                content=message,
                token=token
            )
            if message_response["status"] != "success":
                return message_response

            # Run assistant
            run_response = await self.run_assistant(
                thread_id=thread_id,
                assistant_id=assistant_id,
                instructions=instructions,
                token=token,
                **kwargs
            )
            if run_response["status"] != "success":
                return run_response

            run_id = run_response["run_id"]

            # Wait for completion (simplified - in production you'd want better polling)
            max_attempts = 30
            for attempt in range(max_attempts):
                await asyncio.sleep(1)  # Wait 1 second between checks

                run_status = await client.beta.threads.runs.retrieve(
                    thread_id=thread_id,
                    run_id=run_id
                )

                if run_status.status == "completed":
                    # Get messages
                    messages = await client.beta.threads.messages.list(
                        thread_id=thread_id,
                        limit=1
                    )

                    if messages.data:
                        latest_message = messages.data[0]
                        content = latest_message.content[0].text.value if latest_message.content else ""

                        return {
                            "status": "success",
                            "message": "Assistant response completed",
                            "content": content,
                            "thread_id": thread_id,
                            "run_id": run_id,
                            "assistant_id": assistant_id,
                            "run_status": run_status.status,
                            "full_response": latest_message.model_dump()
                        }

                elif run_status.status in ["failed", "cancelled", "expired"]:
                    return {
                        "status": "error",
                        "error": f"Run {run_status.status}",
                        "message": f"Assistant run {run_status.status}",
                        "thread_id": thread_id,
                        "run_id": run_id,
                        "run_status": run_status.status
                    }

            # Timeout
            return {
                "status": "error",
                "error": "Timeout waiting for assistant response",
                "message": "Assistant did not respond within the timeout period",
                "thread_id": thread_id,
                "run_id": run_id
            }

        except Exception as e:
            logger.error(f"OpenAI assistant message error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to get assistant response"
            }

    # ============================================================================
    # STREAMING SUPPORT
    # ============================================================================

    async def chat_stream(
        self,
        message: Union[str, List[Dict[str, str]]],
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        system_prompt: Optional[str] = None,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a streaming chat completion.
        Note: This returns a placeholder since streaming requires special handling in FastAPI.

        Args:
            message: User message (string) or conversation history (list of messages)
            model: Model to use (default: gpt-4o-mini)
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature (0-2)
            system_prompt: System message to set behavior
            token: OpenAI API key
            **kwargs: Additional OpenAI parameters

        Returns:
            Streaming response placeholder

        Example:
            await openai.chat_stream("Tell me a story")
        """
        try:
            # For now, return a placeholder since streaming requires special FastAPI handling
            return {
                "status": "error",
                "error": "Streaming not implemented in this version",
                "message": "Streaming chat requires special FastAPI response handling",
                "note": "Use the regular 'chat' method for non-streaming responses",
                "parameters": {
                    "message": message if isinstance(message, str) else f"{len(message)} messages",
                    "model": model or self.default_chat_model,
                    "max_tokens": max_tokens,
                    "temperature": temperature,
                    "system_prompt": system_prompt
                }
            }

        except Exception as e:
            logger.error(f"OpenAI chat stream error: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Failed to create streaming chat"
            }

    # ============================================================================
    # UTILITY METHODS
    # ============================================================================

    async def get_usage_stats(
        self,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Get usage statistics (placeholder method).
        Note: OpenAI doesn't provide a direct usage API endpoint.

        Args:
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Usage statistics placeholder

        Example:
            await openai.get_usage_stats()
        """
        return {
            "status": "info",
            "message": "Usage statistics not available via API",
            "note": "Check your OpenAI dashboard for usage statistics",
            "dashboard_url": "https://platform.openai.com/usage",
            "available_methods": [
                "chat", "vision", "generate_image", "text_to_speech",
                "speech_to_text", "create_embeddings", "moderate_content",
                "list_models", "create_assistant", "assistant_message"
            ]
        }

    async def health_check(
        self,
        token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Perform a health check by listing available models.

        Args:
            token: OpenAI API key
            **kwargs: Additional parameters

        Returns:
            Health check response

        Example:
            await openai.health_check()
        """
        try:
            # Try to list models as a health check
            models_response = await self.list_models(token=token)

            if models_response["status"] == "success":
                return {
                    "status": "success",
                    "message": "OpenAI service is healthy",
                    "api_accessible": True,
                    "models_available": models_response["model_count"],
                    "default_models": {
                        "chat": self.default_chat_model,
                        "image": self.default_image_model,
                        "tts": self.default_tts_model,
                        "whisper": self.default_whisper_model,
                        "embedding": self.default_embedding_model
                    }
                }
            else:
                return {
                    "status": "error",
                    "message": "OpenAI service health check failed",
                    "api_accessible": False,
                    "error": models_response.get("error", "Unknown error")
                }

        except Exception as e:
            logger.error(f"OpenAI health check error: {str(e)}")
            return {
                "status": "error",
                "message": "OpenAI service health check failed",
                "api_accessible": False,
                "error": str(e)
            }

    async def get_service_info(self) -> Dict[str, Any]:
        """
        Get information about the OpenAI service.

        Returns:
            Service information

        Example:
            openai.get_service_info()
        """
        return {
            "service_name": "OpenAI API Service",
            "version": "1.0.0",
            "description": "Comprehensive OpenAI API integration service",
            "features": [
                "Chat Completions (GPT models)",
                "Vision Analysis (GPT-4 Vision)",
                "Image Generation (DALL-E)",
                "Text-to-Speech (TTS)",
                "Speech-to-Text (Whisper)",
                "Embeddings",
                "Content Moderation",
                "Assistants API",
                "Model Management"
            ],
            "default_models": {
                "chat": self.default_chat_model,
                "image": self.default_image_model,
                "tts": self.default_tts_model,
                "whisper": self.default_whisper_model,
                "embedding": self.default_embedding_model
            },
            "base_url": self.base_url,
            "organization": self.organization,
            "project": self.project,
            "available_methods": [
                "test_method", "test_chat", "chat", "vision", "generate_image",
                "text_to_speech", "speech_to_text", "translate_audio",
                "create_embeddings", "moderate_content", "list_models", "get_model",
                "create_assistant", "list_assistants", "get_assistant",
                "create_thread", "add_message_to_thread", "run_assistant",
                "assistant_message", "chat_stream", "health_check", "get_service_info"
            ]
        }


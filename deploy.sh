#!/bin/bash
# Deployment script for Common Class API

set -e

echo "🚀 Deploying Common Class API..."

# Pull latest changes (if using git)
if [ -d ".git" ]; then
    echo "📥 Pulling latest changes..."
    git pull origin main
fi

# Build and restart Docker containers
echo "🐳 Building Docker containers..."
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# Wait for service to be ready
echo "⏳ Waiting for service to start..."
sleep 10

# Health check
echo "🔍 Performing health check..."
if curl -f http://localhost:8000/health; then
    echo "✅ Deployment successful!"
    echo "📖 API Documentation: http://localhost:8000/docs"
    echo "🔗 Available endpoints: http://localhost:8000/available-endpoints"
else
    echo "❌ Health check failed!"
    docker-compose logs
    exit 1
fi

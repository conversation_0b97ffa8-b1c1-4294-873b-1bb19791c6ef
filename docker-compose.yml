version: "3.9"

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    env_file: .env
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - PORT=8000
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - api-network

networks:
  api-network:
    driver: bridge

#!/usr/bin/env python3
"""
LINE Service Usage Examples
Demonstrates common use cases for the LINE messaging service.
"""

import requests
import json

# Configuration
API_BASE_URL = "http://127.0.0.1:8000"
API_TOKEN = "8a2c1cf846ba5e1245bfb3cc31a6d60a779ca49f02b65ddce1a355e91d01f8bd"

def call_line_api(method: str, data: dict = None):
    """Helper function to call LINE API methods."""
    url = f"{API_BASE_URL}/line.{method}"
    headers = {
        'Authorization': f'Bearer {API_TOKEN}',
        'Content-Type': 'application/json'
    }
    
    response = requests.post(url, headers=headers, json=data or {})
    return response.json()

def example_1_simple_broadcast():
    """Example 1: Send a simple broadcast message."""
    print("📢 Example 1: Simple Broadcast")
    
    result = call_line_api("broadcast", {
        "messages": "🎉 Hello everyone! This is a test broadcast from our LINE bot."
    })
    
    print(f"Result: {json.dumps(result, indent=2)}")
    print()

def example_2_rich_broadcast():
    """Example 2: Send a rich broadcast with multiple message types."""
    print("🎨 Example 2: Rich Broadcast with Multiple Messages")
    
    messages = [
        {
            "type": "text",
            "text": "Welcome to our service! 🚀"
        },
        {
            "type": "sticker",
            "packageId": "1",
            "stickerId": "1"
        }
    ]
    
    result = call_line_api("broadcast", {
        "messages": messages
    })
    
    print(f"Result: {json.dumps(result, indent=2)}")
    print()

def example_3_send_to_specific_user():
    """Example 3: Send message to a specific user."""
    print("👤 Example 3: Send to Specific User")
    
    # Note: Replace with a real user ID for actual testing
    user_id = "U1234567890abcdef"
    
    result = call_line_api("push", {
        "to": user_id,
        "messages": "Hello! This is a personal message just for you. 😊"
    })
    
    print(f"Result: {json.dumps(result, indent=2)}")
    print()

def example_4_send_image():
    """Example 4: Send an image message."""
    print("🖼️ Example 4: Send Image Message")
    
    user_id = "U1234567890abcdef"
    
    result = call_line_api("send_image", {
        "to": user_id,
        "image_url": "https://picsum.photos/800/600",
        "preview_url": "https://picsum.photos/240/240"
    })
    
    print(f"Result: {json.dumps(result, indent=2)}")
    print()

def example_5_send_location():
    """Example 5: Send a location message."""
    print("📍 Example 5: Send Location")
    
    user_id = "U1234567890abcdef"
    
    result = call_line_api("send_location", {
        "to": user_id,
        "title": "Tokyo Station",
        "address": "1 Chome-9-1 Marunouchi, Chiyoda City, Tokyo",
        "latitude": 35.6812,
        "longitude": 139.7671
    })
    
    print(f"Result: {json.dumps(result, indent=2)}")
    print()

def example_6_multicast():
    """Example 6: Send to multiple users at once."""
    print("👥 Example 6: Multicast to Multiple Users")
    
    user_ids = ["U123", "U456", "U789"]  # Replace with real user IDs
    
    result = call_line_api("multicast", {
        "to": user_ids,
        "messages": "📢 Important announcement for selected users!"
    })
    
    print(f"Result: {json.dumps(result, indent=2)}")
    print()

def example_7_get_bot_info():
    """Example 7: Get bot information."""
    print("🤖 Example 7: Get Bot Information")
    
    result = call_line_api("get_bot_info")
    
    print(f"Bot Info: {json.dumps(result, indent=2)}")
    print()

def example_8_check_quota():
    """Example 8: Check message quota."""
    print("📊 Example 8: Check Message Quota")
    
    quota = call_line_api("get_quota")
    consumption = call_line_api("get_quota_consumption")
    
    print(f"Quota: {json.dumps(quota, indent=2)}")
    print(f"Consumption: {json.dumps(consumption, indent=2)}")
    
    if quota.get('value') and consumption.get('totalUsage'):
        remaining = quota['value'] - consumption['totalUsage']
        print(f"📈 Messages remaining this month: {remaining}")
    print()

def example_9_batch_messages():
    """Example 9: Send different messages to multiple targets."""
    print("🔄 Example 9: Batch Messages")
    
    targets = [
        {
            "to": "U123",
            "messages": "Hello User 1! 👋"
        },
        {
            "to": "U456", 
            "messages": "Hello User 2! 🎉"
        },
        {
            "to": "U789",
            "messages": "Hello User 3! ⭐"
        }
    ]
    
    result = call_line_api("send_multiple_messages", {
        "targets": targets
    })
    
    print(f"Batch Result: {json.dumps(result, indent=2)}")
    print()

def example_10_create_rich_message():
    """Example 10: Create and send a rich message."""
    print("✨ Example 10: Rich Message Creation")
    
    # Create a complex message with location
    location_msg = call_line_api("create_location_message", {
        "title": "Meeting Point",
        "address": "Shibuya Crossing, Tokyo",
        "latitude": 35.6598,
        "longitude": 139.7006
    })
    
    print(f"Created location message: {json.dumps(location_msg, indent=2)}")
    
    # You could then use this message in a broadcast or push
    # result = call_line_api("broadcast", {"messages": [location_msg]})
    print()

def main():
    """Run all examples."""
    print("🚀 LINE Service Usage Examples")
    print("=" * 50)
    
    # Run examples
    example_1_simple_broadcast()
    example_2_rich_broadcast()
    example_3_send_to_specific_user()
    example_4_send_image()
    example_5_send_location()
    example_6_multicast()
    example_7_get_bot_info()
    example_8_check_quota()
    example_9_batch_messages()
    example_10_create_rich_message()
    
    print("✅ All examples completed!")
    print("\n💡 Tips:")
    print("- Replace example user IDs with real ones for actual messaging")
    print("- Monitor your quota usage to avoid hitting limits")
    print("- Use rich messages to create engaging user experiences")
    print("- Test with small groups before broadcasting to all users")

if __name__ == "__main__":
    main()

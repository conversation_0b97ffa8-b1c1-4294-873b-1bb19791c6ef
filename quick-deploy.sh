#!/bin/bash
# Quick deployment script for Common Class API

echo "🚀 Common Class API - Quick Deploy Script"
echo "=========================================="

# Check if we're in the right directory
if [ ! -f "app/main.py" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

echo "📋 Choose deployment option:"
echo "1) Railway (Recommended)"
echo "2) Render"
echo "3) Fly.io"
echo "4) Docker (Local/VPS)"
echo "5) Test locally first"

read -p "Enter choice (1-5): " choice

case $choice in
    1)
        echo "🚂 Deploying to Railway..."
        if ! command -v railway &> /dev/null; then
            echo "Installing Railway CLI..."
            npm install -g @railway/cli
        fi
        railway login
        railway init
        railway up
        echo "✅ Deployed! Set your environment variables in Railway dashboard"
        ;;
    2)
        echo "🎨 Deploying to Render..."
        echo "1. Push code to GitHub"
        echo "2. Connect repository in Render dashboard"
        echo "3. Use render.yaml configuration"
        echo "4. Set custom domain to common_class_api.ultragent.app"
        ;;
    3)
        echo "🪰 Deploying to Fly.io..."
        if ! command -v fly &> /dev/null; then
            echo "Installing Fly CLI..."
            curl -L https://fly.io/install.sh | sh
        fi
        fly auth login
        fly launch
        fly deploy
        ;;
    4)
        echo "🐳 Docker deployment..."
        if command -v docker &> /dev/null; then
            docker-compose up --build -d
            echo "✅ Running on http://localhost:8000"
        else
            echo "❌ Docker not found. Please install Docker first."
        fi
        ;;
    5)
        echo "🧪 Testing locally..."
        if [ ! -d ".venv" ]; then
            python -m venv .venv
        fi
        source .venv/bin/activate || source .venv/Scripts/activate
        pip install -r requirements.txt
        echo "🚀 Starting server..."
        uvicorn app.main:app --reload --host 127.0.0.1 --port 8000
        ;;
    *)
        echo "❌ Invalid choice"
        exit 1
        ;;
esac

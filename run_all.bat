@echo off
REM Common Class API - Run All Script
REM This script sets up and runs the FastAPI application

echo 🚀 Common Class API - Setup and Run
echo.

REM Check if virtual environment exists
if not exist ".venv" (
    echo 🔧 Creating virtual environment...
    python -m venv .venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment and install dependencies
echo 📦 Installing dependencies...
call .venv\Scripts\activate.bat
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

REM Create .env file if it doesn't exist
if not exist ".env" (
    echo ⚙️  Creating environment configuration...
    copy .env.example .env
)

REM Start the application
echo.
echo 🚀 Starting FastAPI application...
echo 📖 API Documentation will be available at: http://127.0.0.1:8000/docs
echo 🛑 Press Ctrl+C to stop the server
echo.
uvicorn app.main:app --reload --host 127.0.0.1 --port 8000

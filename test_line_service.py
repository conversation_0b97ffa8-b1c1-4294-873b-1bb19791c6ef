#!/usr/bin/env python3
"""
Test script for the complete LINE messaging service.
This script demonstrates all available LINE API functionality.
"""

import requests
import json
import asyncio
from typing import Dict, Any

# Configuration
API_BASE_URL = "http://127.0.0.1:8000"
API_TOKEN = "8a2c1cf846ba5e1245bfb3cc31a6d60a779ca49f02b65ddce1a355e91d01f8bd"

def make_api_call(endpoint: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
    """Make API call to LINE service endpoint."""
    url = f"{API_BASE_URL}/line.{endpoint}"
    headers = {
        'Authorization': f'Bearer {API_TOKEN}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.post(url, headers=headers, json=data or {})
        return {
            "status_code": response.status_code,
            "response": response.json() if response.text else {}
        }
    except Exception as e:
        return {"error": str(e)}

def test_bot_info():
    """Test getting bot information."""
    print("🤖 Testing bot information...")
    result = make_api_call("get_bot_info")
    print(f"Status: {result.get('status_code')}")
    if result.get('response'):
        bot_info = result['response']
        print(f"Bot Name: {bot_info.get('displayName')}")
        print(f"Bot ID: {bot_info.get('userId')}")
        print(f"Basic ID: {bot_info.get('basicId')}")
    print()

def test_quota_info():
    """Test getting quota information."""
    print("📊 Testing quota information...")
    
    # Test quota
    result = make_api_call("get_quota")
    print(f"Quota Status: {result.get('status_code')}")
    if result.get('response'):
        print(f"Quota Response: {json.dumps(result['response'], indent=2)}")
    
    # Test quota consumption
    result = make_api_call("get_quota_consumption")
    print(f"Consumption Status: {result.get('status_code')}")
    if result.get('response'):
        print(f"Consumption Response: {json.dumps(result['response'], indent=2)}")
    print()

def test_message_creation():
    """Test message creation helpers."""
    print("💬 Testing message creation helpers...")
    
    # Test text message
    result = make_api_call("create_text_message", {
        "text": "Hello from LINE service test!"
    })
    print(f"Text message creation: {result}")
    
    # Test image message
    result = make_api_call("create_image_message", {
        "original_content_url": "https://example.com/image.jpg",
        "preview_image_url": "https://example.com/preview.jpg"
    })
    print(f"Image message creation: {result}")
    print()

def test_broadcast():
    """Test broadcast messaging."""
    print("📢 Testing broadcast messaging...")
    
    # Simple text broadcast
    result = make_api_call("broadcast", {
        "messages": "Hello everyone! This is a test broadcast from the complete LINE service."
    })
    print(f"Broadcast Status: {result.get('status_code')}")
    print(f"Broadcast Response: {json.dumps(result.get('response', {}), indent=2)}")
    print()

def test_convenience_methods():
    """Test convenience methods."""
    print("🎯 Testing convenience methods...")
    
    # Note: These would need a real user ID to work
    test_user_id = "U1234567890abcdef"  # Example user ID
    
    # Test send_text (would fail without real user ID)
    result = make_api_call("send_text", {
        "to": test_user_id,
        "text": "Hello from convenience method!"
    })
    print(f"Send text status: {result.get('status_code')}")
    
    # Test send_sticker
    result = make_api_call("send_sticker", {
        "to": test_user_id,
        "package_id": "1",
        "sticker_id": "1"
    })
    print(f"Send sticker status: {result.get('status_code')}")
    print()

def main():
    """Run all tests."""
    print("🚀 LINE Service Complete Test Suite")
    print("=" * 50)
    
    # Test basic functionality
    test_bot_info()
    test_quota_info()
    test_message_creation()
    test_broadcast()
    test_convenience_methods()
    
    print("✅ Test suite completed!")
    print("\n📋 Available LINE Service Methods:")
    print("   Core Messaging:")
    print("   - line.broadcast")
    print("   - line.push") 
    print("   - line.reply")
    print("   - line.multicast")
    print()
    print("   Message Helpers:")
    print("   - line.create_text_message")
    print("   - line.create_image_message")
    print("   - line.create_video_message")
    print("   - line.create_audio_message")
    print("   - line.create_location_message")
    print("   - line.create_sticker_message")
    print()
    print("   User Management:")
    print("   - line.get_profile")
    print("   - line.get_group_member_profile")
    print("   - line.get_room_member_profile")
    print("   - line.leave_group")
    print("   - line.leave_room")
    print()
    print("   Utilities:")
    print("   - line.get_bot_info")
    print("   - line.get_quota")
    print("   - line.get_quota_consumption")
    print("   - line.validate_webhook_signature")
    print()
    print("   Convenience Methods:")
    print("   - line.send_text")
    print("   - line.send_image")
    print("   - line.send_sticker")
    print("   - line.send_location")
    print("   - line.send_multiple_messages")

if __name__ == "__main__":
    main()
